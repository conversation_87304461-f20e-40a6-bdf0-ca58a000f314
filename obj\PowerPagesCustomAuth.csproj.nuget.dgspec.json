{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Osler Programming\\Entra Password History - Revised 1\\PowerPagesCustomAuth\\PowerPagesCustomAuth.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Osler Programming\\Entra Password History - Revised 1\\PowerPagesCustomAuth\\PowerPagesCustomAuth.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Osler Programming\\Entra Password History - Revised 1\\PowerPagesCustomAuth\\PowerPagesCustomAuth.csproj", "projectName": "PowerPagesCustomAuth", "projectPath": "C:\\Users\\<USER>\\Documents\\Osler Programming\\Entra Password History - Revised 1\\PowerPagesCustomAuth\\PowerPagesCustomAuth.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Osler Programming\\Entra Password History - Revised 1\\PowerPagesCustomAuth\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Azure.Extensions.AspNetCore.Configuration.Secrets": {"target": "Package", "version": "[1.3.2, )"}, "Azure.Identity": {"target": "Package", "version": "[1.14.0, )"}, "Azure.Storage.Blobs": {"target": "Package", "version": "[12.19.1, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.ApplicationInsights.WorkerService": {"target": "Package", "version": "[2.22.0, )"}, "Microsoft.Azure.Functions.Worker": {"target": "Package", "version": "[1.23.0, )"}, "Microsoft.Azure.Functions.Worker.ApplicationInsights": {"target": "Package", "version": "[1.4.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Http": {"target": "Package", "version": "[3.2.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore": {"target": "Package", "version": "[1.3.2, )"}, "Microsoft.Azure.Functions.Worker.Sdk": {"target": "Package", "version": "[1.18.1, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.AzureKeyVault": {"target": "Package", "version": "[3.1.24, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Http.Resilience": {"target": "Package", "version": "[8.9.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.ApplicationInsights": {"target": "Package", "version": "[2.22.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.78.0, )"}, "Microsoft.IdentityModel.JsonWebTokens": {"include": "None", "target": "Package", "version": "[8.6.1, )"}, "Microsoft.IdentityModel.Tokens": {"include": "None", "target": "Package", "version": "[8.6.1, )"}, "SendGrid": {"target": "Package", "version": "[9.29.3, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"include": "None", "target": "Package", "version": "[8.6.1, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}