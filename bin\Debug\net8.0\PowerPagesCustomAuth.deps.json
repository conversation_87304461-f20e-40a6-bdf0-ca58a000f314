{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"PowerPagesCustomAuth/1.0.0": {"dependencies": {"Azure.Extensions.AspNetCore.Configuration.Secrets": "1.3.2", "Azure.Identity": "1.14.0", "Azure.Storage.Blobs": "12.19.1", "BCrypt.Net-Next": "4.0.3", "Microsoft.ApplicationInsights.WorkerService": "2.22.0", "Microsoft.Azure.Functions.Worker": "1.23.0", "Microsoft.Azure.Functions.Worker.ApplicationInsights": "1.4.0", "Microsoft.Azure.Functions.Worker.Extensions.Http": "3.2.0", "Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore": "1.3.2", "Microsoft.Azure.Functions.Worker.Sdk": "1.18.1", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.AzureKeyVault": "3.1.24", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Hosting": "9.0.0", "Microsoft.Extensions.Http": "9.0.0", "Microsoft.Extensions.Http.Resilience": "8.9.1", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Graph": "5.78.0", "Microsoft.IdentityModel.JsonWebTokens": "8.6.1", "Microsoft.IdentityModel.Tokens": "8.6.1", "SendGrid": "9.29.3", "System.ComponentModel.Annotations": "5.0.0", "System.IdentityModel.Tokens.Jwt": "8.6.1", "System.Text.Json": "9.0.0"}, "runtime": {"PowerPagesCustomAuth.dll": {}}}, "Azure.Core/1.46.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.4.1", "System.Memory.Data": "6.0.1"}, "runtime": {"lib/net8.0/Azure.Core.dll": {"assemblyVersion": "********", "fileVersion": "1.4600.125.25909"}}}, "Azure.Extensions.AspNetCore.Configuration.Secrets/1.3.2": {"dependencies": {"Azure.Core": "1.46.1", "Azure.Security.KeyVault.Secrets": "4.6.0", "Microsoft.Extensions.Configuration": "9.0.0"}, "runtime": {"lib/netstandard2.0/Azure.Extensions.AspNetCore.Configuration.Secrets.dll": {"assemblyVersion": "*******", "fileVersion": "1.300.224.41501"}}}, "Azure.Identity/1.14.0": {"dependencies": {"Azure.Core": "1.46.1", "Microsoft.Identity.Client": "4.71.1", "Microsoft.Identity.Client.Extensions.Msal": "4.71.1", "System.Memory": "4.5.5"}, "runtime": {"lib/net8.0/Azure.Identity.dll": {"assemblyVersion": "1.14.0.0", "fileVersion": "1.1400.25.26304"}}}, "Azure.Security.KeyVault.Secrets/4.6.0": {"dependencies": {"Azure.Core": "1.46.1", "System.Memory": "4.5.5", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"assemblyVersion": "4.6.0.0", "fileVersion": "4.600.24.11403"}}}, "Azure.Storage.Blobs/12.19.1": {"dependencies": {"Azure.Storage.Common": "12.18.1", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net6.0/Azure.Storage.Blobs.dll": {"assemblyVersion": "12.19.1.0", "fileVersion": "12.1900.123.56305"}}}, "Azure.Storage.Common/12.18.1": {"dependencies": {"Azure.Core": "1.46.1", "System.IO.Hashing": "6.0.0"}, "runtime": {"lib/net6.0/Azure.Storage.Common.dll": {"assemblyVersion": "12.18.1.0", "fileVersion": "12.1800.123.56305"}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "Google.Protobuf/3.25.2": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.25.2.0", "fileVersion": "3.25.2.0"}}}, "Grpc.Core.Api/2.60.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "*******", "fileVersion": "2.60.0.0"}}}, "Grpc.Net.Client/2.60.0": {"dependencies": {"Grpc.Net.Common": "2.60.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "2.60.0.0"}}}, "Grpc.Net.ClientFactory/2.60.0": {"dependencies": {"Grpc.Net.Client": "2.60.0", "Microsoft.Extensions.Http": "9.0.0"}, "runtime": {"lib/net8.0/Grpc.Net.ClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "2.60.0.0"}}}, "Grpc.Net.Common/2.60.0": {"dependencies": {"Grpc.Core.Api": "2.60.0"}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"assemblyVersion": "*******", "fileVersion": "2.60.0.0"}}}, "Microsoft.ApplicationInsights/2.22.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.ApplicationInsights.DependencyCollector/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AI.DependencyCollector.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.ApplicationInsights.EventCounterCollector/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AI.EventCounterCollector.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.ApplicationInsights.PerfCounterCollector/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "System.Diagnostics.PerformanceCounter": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AI.PerfCounterCollector.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.ApplicationInsights.WindowsServer/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.22.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.22.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AI.WindowsServer.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "System.IO.FileSystem.AccessControl": "4.7.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AI.ServerTelemetryChannel.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.ApplicationInsights.WorkerService/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.22.0", "Microsoft.ApplicationInsights.EventCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.22.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.WorkerService.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.Azure.Functions.Worker/1.23.0": {"dependencies": {"Azure.Core": "1.46.1", "Microsoft.Azure.Functions.Worker.Core": "1.19.0", "Microsoft.Azure.Functions.Worker.Grpc": "1.17.0", "Microsoft.Extensions.Hosting": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0"}, "runtime": {"lib/net7.0/Microsoft.Azure.Functions.Worker.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Azure.Functions.Worker.ApplicationInsights/1.4.0": {"dependencies": {"Azure.Identity": "1.14.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.22.0", "Microsoft.Azure.Functions.Worker.Core": "1.19.0", "Microsoft.Bcl.AsyncInterfaces": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.Functions.Worker.ApplicationInsights.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Azure.Functions.Worker.Core/1.19.0": {"dependencies": {"Azure.Core": "1.46.1", "Microsoft.Extensions.Hosting": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "System.Collections.Immutable": "5.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net5.0/Microsoft.Azure.Functions.Worker.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Azure.Functions.Worker.Extensions.Abstractions/1.3.0": {"runtime": {"lib/netstandard2.0/Microsoft.Azure.Functions.Worker.Extensions.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Azure.Functions.Worker.Extensions.Http/3.2.0": {"dependencies": {"Microsoft.Azure.Functions.Worker.Core": "1.19.0", "Microsoft.Azure.Functions.Worker.Extensions.Abstractions": "1.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.Functions.Worker.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore/1.3.2": {"dependencies": {"Microsoft.Azure.Functions.Worker": "1.23.0", "Microsoft.Azure.Functions.Worker.Extensions.Abstractions": "1.3.0", "Microsoft.Azure.Functions.Worker.Extensions.Http": "3.2.0", "Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore.Analyzers": "1.0.2"}, "runtime": {"lib/net6.0/Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore.Analyzers/1.0.2": {}, "Microsoft.Azure.Functions.Worker.Grpc/1.17.0": {"dependencies": {"Azure.Core": "1.46.1", "Google.Protobuf": "3.25.2", "Grpc.Net.Client": "2.60.0", "Grpc.Net.ClientFactory": "2.60.0", "Microsoft.Azure.Functions.Worker.Core": "1.19.0", "Microsoft.Azure.Functions.Worker.Extensions.Abstractions": "1.3.0", "Microsoft.Extensions.Hosting": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0"}, "runtime": {"lib/net7.0/Microsoft.Azure.Functions.Worker.Grpc.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Azure.Functions.Worker.Sdk/1.18.1": {"dependencies": {"Microsoft.Azure.Functions.Worker.Sdk.Analyzers": "1.2.1", "Microsoft.Azure.Functions.Worker.Sdk.Generators": "1.3.4"}}, "Microsoft.Azure.Functions.Worker.Sdk.Analyzers/1.2.1": {}, "Microsoft.Azure.Functions.Worker.Sdk.Generators/1.3.4": {}, "Microsoft.Azure.KeyVault/2.3.2": {"dependencies": {"Microsoft.Azure.KeyVault.WebKey": "2.0.7", "Microsoft.Rest.ClientRuntime": "2.3.8", "Microsoft.Rest.ClientRuntime.Azure": "3.3.7", "NETStandard.Library": "1.6.1", "Newtonsoft.Json": "13.0.1", "System.Net.Http": "4.3.0"}, "runtime": {"lib/netstandard1.4/Microsoft.Azure.KeyVault.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Azure.KeyVault.WebKey/2.0.7": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "NETStandard.Library": "1.6.1", "Newtonsoft.Json": "13.0.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Runtime": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0"}, "runtime": {"lib/netstandard1.4/Microsoft.Azure.KeyVault.WebKey.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.7.0"}}}, "Microsoft.Azure.Services.AppAuthentication/1.0.3": {"dependencies": {"Microsoft.IdentityModel.Clients.ActiveDirectory": "3.14.2", "NETStandard.Library": "1.6.1", "System.Diagnostics.Process": "4.3.0"}, "runtime": {"lib/netstandard1.4/Microsoft.Azure.Services.AppAuthentication.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.Memory/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Bcl.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.AmbientMetadata.Application/8.9.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "*******", "fileVersion": "8.900.124.45604"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Compliance.Abstractions/8.9.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.ObjectPool": "8.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.900.124.45604"}}}, "Microsoft.Extensions.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.AzureKeyVault/3.1.24": {"dependencies": {"Microsoft.Azure.KeyVault": "2.3.2", "Microsoft.Azure.Services.AppAuthentication": "1.0.3", "Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.AzureKeyVault.dll": {"assemblyVersion": "3.1.24.0", "fileVersion": "3.100.2422.18002"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/8.9.1": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "*******", "fileVersion": "8.900.124.45604"}}}, "Microsoft.Extensions.Diagnostics/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/8.9.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "*******", "fileVersion": "8.900.124.45604"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Hosting/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Configuration": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Logging.Debug": "9.0.0", "Microsoft.Extensions.Logging.EventLog": "9.0.0", "Microsoft.Extensions.Logging.EventSource": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Http/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Http.Diagnostics/8.9.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "8.9.1", "Microsoft.Extensions.Http": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.Extensions.Telemetry": "8.9.1", "Microsoft.IO.RecyclableMemoryStream": "3.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.900.124.45604"}}}, "Microsoft.Extensions.Http.Resilience/8.9.1": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Http.Diagnostics": "8.9.1", "Microsoft.Extensions.ObjectPool": "8.0.8", "Microsoft.Extensions.Resilience": "8.9.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "8.900.124.45604"}}}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.ApplicationInsights/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Logging": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.ApplicationInsights.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Console/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Configuration": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Diagnostics.EventLog": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.ObjectPool/8.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}}}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Primitives/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Resilience/8.9.1": {"dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.0", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "8.9.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Microsoft.Extensions.Telemetry.Abstractions": "8.9.1", "Polly.Extensions": "8.4.1", "Polly.RateLimiting": "8.4.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "8.900.124.45604"}}}, "Microsoft.Extensions.Telemetry/8.9.1": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "8.9.1", "Microsoft.Extensions.DependencyInjection.AutoActivation": "8.9.1", "Microsoft.Extensions.Logging.Configuration": "9.0.0", "Microsoft.Extensions.ObjectPool": "8.0.8", "Microsoft.Extensions.Telemetry.Abstractions": "8.9.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "8.900.124.45604"}}}, "Microsoft.Extensions.Telemetry.Abstractions/8.9.1": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "8.9.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.ObjectPool": "8.0.8", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.900.124.45604"}}}, "Microsoft.Graph/5.78.0": {"dependencies": {"Microsoft.Graph.Core": "3.2.4"}, "runtime": {"lib/net5.0/Microsoft.Graph.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Graph.Core/3.2.4": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.6.1", "Microsoft.IdentityModel.Validators": "8.6.1", "Microsoft.Kiota.Abstractions": "1.17.1", "Microsoft.Kiota.Authentication.Azure": "1.17.1", "Microsoft.Kiota.Http.HttpClientLibrary": "1.17.1", "Microsoft.Kiota.Serialization.Form": "1.17.1", "Microsoft.Kiota.Serialization.Json": "1.17.1", "Microsoft.Kiota.Serialization.Multipart": "1.17.1", "Microsoft.Kiota.Serialization.Text": "1.17.1"}, "runtime": {"lib/net6.0/Microsoft.Graph.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Identity.Client/4.71.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.6.1", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.71.1": {"dependencies": {"Microsoft.Identity.Client": "4.71.1", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.IdentityModel.Abstractions/8.6.1": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Clients.ActiveDirectory/3.14.2": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Runtime.Serialization.Json": "4.0.2", "System.Runtime.Serialization.Primitives": "4.1.1"}, "runtime": {"lib/netstandard1.3/Microsoft.IdentityModel.Clients.ActiveDirectory.Platform.dll": {"assemblyVersion": "3.14.2.11", "fileVersion": "3.14.40721.918"}, "lib/netstandard1.3/Microsoft.IdentityModel.Clients.ActiveDirectory.dll": {"assemblyVersion": "3.14.2.11", "fileVersion": "3.14.40721.918"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.6.1"}}, "Microsoft.IdentityModel.Logging/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Protocols/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.6.1", "System.IdentityModel.Tokens.Jwt": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Tokens/8.6.1": {"dependencies": {"Microsoft.Bcl.Memory": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.IdentityModel.Logging": "8.6.1"}}, "Microsoft.IdentityModel.Validators/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.6.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.6.1", "Microsoft.IdentityModel.Tokens": "8.6.1", "System.IdentityModel.Tokens.Jwt": "8.6.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Validators.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Kiota.Abstractions/1.17.1": {"dependencies": {"Std.UriTemplate": "2.0.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Authentication.Azure/1.17.1": {"dependencies": {"Azure.Core": "1.46.1", "Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Authentication.Azure.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Http.HttpClientLibrary/1.17.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Http.HttpClientLibrary.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Form/1.17.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Form.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Json/1.17.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Json.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Multipart/1.17.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Multipart.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Kiota.Serialization.Text/1.17.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.17.1"}, "runtime": {"lib/net8.0/Microsoft.Kiota.Serialization.Text.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Rest.ClientRuntime/2.3.8": {"dependencies": {"NETStandard.Library": "1.6.1", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard1.4/Microsoft.Rest.ClientRuntime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Rest.ClientRuntime.Azure/3.3.7": {"dependencies": {"Microsoft.Rest.ClientRuntime": "2.3.8", "NETStandard.Library": "1.6.1", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard1.4/Microsoft.Rest.ClientRuntime.Azure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.1.25517"}}}, "Polly.Core/8.4.1": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.1.3582"}}}, "Polly.Extensions/8.4.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Polly.Core": "8.4.1"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.1.3582"}}}, "Polly.RateLimiting/8.4.1": {"dependencies": {"Polly.Core": "8.4.1", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.1.3582"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "SendGrid/9.29.3": {"dependencies": {"Newtonsoft.Json": "13.0.1", "starkbank-ecdsa": "1.3.3"}, "runtime": {"lib/netstandard2.0/SendGrid.dll": {"assemblyVersion": "9.29.3.0", "fileVersion": "9.29.3.0"}}}, "starkbank-ecdsa/1.3.3": {"runtime": {"lib/netstandard2.1/StarkbankEcdsa.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Std.UriTemplate/2.0.1": {"runtime": {"lib/net5.0/Std.UriTemplate.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.ClientModel/1.4.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.0", "System.Memory.Data": "6.0.1"}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"assemblyVersion": "1.4.1.0", "fileVersion": "1.400.125.25905"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/5.0.0": {}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Configuration.ConfigurationManager/6.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/9.0.0": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Diagnostics.EventLog/9.0.0": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Diagnostics.PerformanceCounter/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Diagnostics.Process/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "Microsoft.Win32.Registry": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/8.6.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.6.1", "Microsoft.IdentityModel.Tokens": "8.6.1"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Hashing/6.0.0": {"runtime": {"lib/net6.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.IO.Pipelines/9.0.0": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.Memory.Data/6.0.1": {"runtime": {"lib/net6.0/System.Memory.Data.dll": {"assemblyVersion": "6.0.0.1", "fileVersion": "6.0.3624.51421"}}}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "9.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.DataContractSerialization/4.1.1": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.1.1", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.0.1", "System.Xml.XmlSerializer": "4.0.11"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Json/4.0.2": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.1.1", "System.Runtime": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.1.1": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/9.0.0": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Text.Json/9.0.0": {"dependencies": {"System.IO.Pipelines": "9.0.0", "System.Text.Encodings.Web": "9.0.0"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.RateLimiting/8.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.0.1": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlSerializer/4.0.11": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.0.1"}}}}, "libraries": {"PowerPagesCustomAuth/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.46.1": {"type": "package", "serviceable": true, "sha512": "sha512-iE5DPOlGsN5kCkF4gN+vasN1RihO0Ypie92oQ5tohQYiokmnrrhLnee+3zcE8n7vB6ZAzhPTfUGAEXX/qHGkYA==", "path": "azure.core/1.46.1", "hashPath": "azure.core.1.46.1.nupkg.sha512"}, "Azure.Extensions.AspNetCore.Configuration.Secrets/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-uMIU++B4XV4lW8+59rKi2Qph6tj2V7tHyLcwVR7OQRmA8cX0VVFhj2DyMahhoF9j4Jk99WA08Nsznd5RwC5Zfw==", "path": "azure.extensions.aspnetcore.configuration.secrets/1.3.2", "hashPath": "azure.extensions.aspnetcore.configuration.secrets.1.3.2.nupkg.sha512"}, "Azure.Identity/1.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-xQ6mpNhifb8W/KG2BclhbJWAupvE3JF8lPEBF8t59Q5sc1yN0Ci+dvS0qXtc6m9auxwYpmc8rhOmK541dcGwmA==", "path": "azure.identity/1.14.0", "hashPath": "azure.identity.1.14.0.nupkg.sha512"}, "Azure.Security.KeyVault.Secrets/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-vwPceoznuT6glvirZcXlaCQrh1uzTSxpZUi2hRFNumHiS3hVyqIXI5fgWiLtlBzwqPJMTr0flUoSvGKjXXQlfg==", "path": "azure.security.keyvault.secrets/4.6.0", "hashPath": "azure.security.keyvault.secrets.4.6.0.nupkg.sha512"}, "Azure.Storage.Blobs/12.19.1": {"type": "package", "serviceable": true, "sha512": "sha512-x43hWFJ4sPQ23TD4piCwT+KlQpZT8pNDAzqj6yUCqh+WJ2qcQa17e1gh6ZOeT2QNFQTTDSuR56fm2bIV7i11/w==", "path": "azure.storage.blobs/12.19.1", "hashPath": "azure.storage.blobs.12.19.1.nupkg.sha512"}, "Azure.Storage.Common/12.18.1": {"type": "package", "serviceable": true, "sha512": "sha512-ohCslqP9yDKIn+DVjBEOBuieB1QwsUCz+BwHYNaJ3lcIsTSiI4Evnq81HcKe8CqM8qvdModbipVQKpnxpbdWqA==", "path": "azure.storage.common/12.18.1", "hashPath": "azure.storage.common.12.18.1.nupkg.sha512"}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "Google.Protobuf/3.25.2": {"type": "package", "serviceable": true, "sha512": "sha512-g/xIIeLhR77bl4ajGGbPYRmZf5acJlCRaSBqUnU8+2aQYREnZnv+UccRNndHof4mWO1ORS0b5i6on40VOjBpsg==", "path": "google.protobuf/3.25.2", "hashPath": "google.protobuf.3.25.2.nupkg.sha512"}, "Grpc.Core.Api/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-VWah+8dGJhhsay5BQ/Ljq6GYDWj0lSjdzqyoBgUQhXTbBqhs+q5dRFROKxI1xxzlL4pfUO45cf/y+KnHVFG9ew==", "path": "grpc.core.api/2.60.0", "hashPath": "grpc.core.api.2.60.0.nupkg.sha512"}, "Grpc.Net.Client/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-J9U96gjZHOcqSgAThg9vZZhLsbTD005bUggPtMP/RVQnGc3+tQJTpkRUCJtJWq9cykNydsRVoyU38TjPP/VJ4A==", "path": "grpc.net.client/2.60.0", "hashPath": "grpc.net.client.2.60.0.nupkg.sha512"}, "Grpc.Net.ClientFactory/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-lCUZjbv6TtI1FbOUE593PLCkXqW2Yrf3KsFfzxIMdJ54o4ELwCEQ26xjfrm+hMTefiXxvXf9FO7zL1rGLWWSig==", "path": "grpc.net.clientfactory/2.60.0", "hashPath": "grpc.net.clientfactory.2.60.0.nupkg.sha512"}, "Grpc.Net.Common/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/917aplgD1RA0q1cd9WpnMGyl9Luu3WZl6ZMpPvNQwg2TNw/3uXUDSriDBybeCtxnKUCtxUcWO3WsVkhM1DcA==", "path": "grpc.net.common/2.60.0", "hashPath": "grpc.net.common.2.60.0.nupkg.sha512"}, "Microsoft.ApplicationInsights/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-3AOM9bZtku7RQwHyMEY3tQMrHIgjcfRDa6YQpd/QG2LDGvMydSlL9Di+8LLMt7J2RDdfJ7/2jdYv6yHcMJAnNw==", "path": "microsoft.applicationinsights/2.22.0", "hashPath": "microsoft.applicationinsights.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.DependencyCollector/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-gseSmuCshdZqcn5r6EW1Zx52e5/p2RpAsHSanlxs8pq+Pbg1RZP678tXtxfVuHC0fA3MVV852pnfFC7ZGB0jew==", "path": "microsoft.applicationinsights.dependencycollector/2.22.0", "hashPath": "microsoft.applicationinsights.dependencycollector.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.EventCounterCollector/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-/fXUyZIMwaWfETgire4fygaBhY8J+hXvTVhSFXKV0JOFBenzzU4smGW8iRUFhE534a3QrczuFfmfCCkXRKbsNg==", "path": "microsoft.applicationinsights.eventcountercollector/2.22.0", "hashPath": "microsoft.applicationinsights.eventcountercollector.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.PerfCounterCollector/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-nExsJsbN7694ueUNNBms/UNgza9WH4W/I6i5CnF9ujJ1sp57EL5Uk0NP9MDwlLVtYaaiznKPatVSv3Nu8vAplw==", "path": "microsoft.applicationinsights.perfcountercollector/2.22.0", "hashPath": "microsoft.applicationinsights.perfcountercollector.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.WindowsServer/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-9k1x1+Kq1fElvcv0o/w9w8tRWAa2Y0f4NPBeHF5b2xCety4GM1yv3K3Ra0lZwO3kW0SHlm9M8nrySuyKQlHyYA==", "path": "microsoft.applicationinsights.windowsserver/2.22.0", "hashPath": "microsoft.applicationinsights.windowsserver.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-Blb6S8UJSJ/jo6mxeO38gKgui75D2brp5NpXJoZUhyJzfmYsfhn7a4t5f+CDfAKyvie7sQB2FIzeEDQSiRE5zw==", "path": "microsoft.applicationinsights.windowsserver.telemetrychannel/2.22.0", "hashPath": "microsoft.applicationinsights.windowsserver.telemetrychannel.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.WorkerService/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-CXwd2s8gOXF9bs0Rn1iAggyNxW9HDbiUIikjtiUOPJW7MMgpEzFk27pJyakMijMHrqCptElTK+C6TWwUmoRLqw==", "path": "microsoft.applicationinsights.workerservice/2.22.0", "hashPath": "microsoft.applicationinsights.workerservice.2.22.0.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker/1.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-9vae0oi6vr0D8A+uwD9znfLF5F/Zmrv3vvXS/l0BvreVZ6ozeAfQM0w70g4VYIyshS7L6w87X3p1cC4f/nbHCA==", "path": "microsoft.azure.functions.worker/1.23.0", "hashPath": "microsoft.azure.functions.worker.1.23.0.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.ApplicationInsights/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-pO5SapJonK9/AxKClmHatFteIp4AJW4KINDq2uHwtBKV0dcgO1ey7IKu27jRbEceIAeso1zfLvlfL5JIdDgtAQ==", "path": "microsoft.azure.functions.worker.applicationinsights/1.4.0", "hashPath": "microsoft.azure.functions.worker.applicationinsights.1.4.0.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Core/1.19.0": {"type": "package", "serviceable": true, "sha512": "sha512-srGJHtJNJRI/cxBkKSZd9+mA8V8nBbsCx3mwFBZKY2M6/EF3mP8EoUosd4W8AjrKOAEXjgXVEp3K0fQyPXcquA==", "path": "microsoft.azure.functions.worker.core/1.19.0", "hashPath": "microsoft.azure.functions.worker.core.1.19.0.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Extensions.Abstractions/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6+/Yb/ouWUweaSQhesbbiIVSmwYEzkSfjIHrBnNqIiCYnx2iLeoYyWjN/wHP3Fnn5COtyDXRDwHKr5A/tCL9Q==", "path": "microsoft.azure.functions.worker.extensions.abstractions/1.3.0", "hashPath": "microsoft.azure.functions.worker.extensions.abstractions.1.3.0.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Extensions.Http/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-TC1xa/s1hZ97ertSh0+qZVjeXB632ObjsuVEhytND32kLfJhT7u+AiBunvdACgn6r0WOZeLIa/BZPA7qovFxiw==", "path": "microsoft.azure.functions.worker.extensions.http/3.2.0", "hashPath": "microsoft.azure.functions.worker.extensions.http.3.2.0.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-46XoRpXWeavwdg7nJt6AmcfowIOsx8AwQAeaszyqKQ/wXxvarEXGdJtkmu1+ooL6ugk4+BAqJ5FKE80kiDYWOA==", "path": "microsoft.azure.functions.worker.extensions.http.aspnetcore/1.3.2", "hashPath": "microsoft.azure.functions.worker.extensions.http.aspnetcore.1.3.2.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore.Analyzers/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-PrB2dW3LiEn3RdWyLMVQ5U6K497GMwu7TJxfakvV6OYP7nc3kbhDs0ekEtC4TfMcOMzz2B6Y8JTN4ofM2jQ9wQ==", "path": "microsoft.azure.functions.worker.extensions.http.aspnetcore.analyzers/1.0.2", "hashPath": "microsoft.azure.functions.worker.extensions.http.aspnetcore.analyzers.1.0.2.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Grpc/1.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xv/QSD3C6LEIjAL8tXG4J2nA1zTCa6fIzuIww7OVdBJPBCDkS48IE5MtDiFWpEr0B6idP54L5nYjs5oi8P7dNQ==", "path": "microsoft.azure.functions.worker.grpc/1.17.0", "hashPath": "microsoft.azure.functions.worker.grpc.1.17.0.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Sdk/1.18.1": {"type": "package", "serviceable": true, "sha512": "sha512-1wpn70uKpc5/xD5bn9n9/mW9uYmshsRloCqnzLhI6UOy5+PAJUhF5JUobn5GPrs8ZTbj4i6h8lOCUPPXX8sNhg==", "path": "microsoft.azure.functions.worker.sdk/1.18.1", "hashPath": "microsoft.azure.functions.worker.sdk.1.18.1.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Sdk.Analyzers/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-EOZqyNc718vKJAg4/cgmDqnQg3wYoNKE14NjskBrDAINMBKfujQqHRn4McSFfSHgCXUE0F/Yc4+sh7i6HhbPbg==", "path": "microsoft.azure.functions.worker.sdk.analyzers/1.2.1", "hashPath": "microsoft.azure.functions.worker.sdk.analyzers.1.2.1.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Sdk.Generators/1.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-viNRmVxE5Woviu/kQem3M6qujauuRjbgGbnrrYvXNVdck2opsmhcpr8DfZx9hWzkImsAxLUBy9UTVsaeLCX/yw==", "path": "microsoft.azure.functions.worker.sdk.generators/1.3.4", "hashPath": "microsoft.azure.functions.worker.sdk.generators.1.3.4.nupkg.sha512"}, "Microsoft.Azure.KeyVault/2.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-A82ESUdfLz2wMhYuPxrwf/fA7JVt3IARgeMCG3TsaLtxUxa9RBKX3f0zdnKmvBvJ/u1/5g03OLR26GPekqY5HQ==", "path": "microsoft.azure.keyvault/2.3.2", "hashPath": "microsoft.azure.keyvault.2.3.2.nupkg.sha512"}, "Microsoft.Azure.KeyVault.WebKey/2.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-MVSYao62R9rwl9KF+IsJm+XBLupJj1ma2lfwNeFlSWziXGAopnYK+YkDWqABOqNIV9kpza/MvNBxITzhlJIyIw==", "path": "microsoft.azure.keyvault.webkey/2.0.7", "hashPath": "microsoft.azure.keyvault.webkey.2.0.7.nupkg.sha512"}, "Microsoft.Azure.Services.AppAuthentication/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ywpQaK1klu1IoX4VUf+TBmU4kR71aWNI6O5rEIJU8z28L2xhJhnIm7k2Nf1Zu/PygeuOtt5g0QPCk5+lLltbeQ==", "path": "microsoft.azure.services.appauthentication/1.0.3", "hashPath": "microsoft.azure.services.appauthentication.1.0.3.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bTUtGfpGyJnohQzjdXbtc7MqNzkv7CWUSRz54+ucNm0i32rZiIU0VdVPHDBShOl1qhVKRjW8mnEBz3d2vH93tQ==", "path": "microsoft.bcl.memory/9.0.0", "hashPath": "microsoft.bcl.memory.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/8.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-9na5Z6N+Yq6KQGaX+4JrpNYSU/huXSNHwx1EcMkBU7hpKk6vkyDFixBNM0ttxxmo7KMCvpvzo8MnYH38y7oadQ==", "path": "microsoft.extensions.ambientmetadata.application/8.9.1", "hashPath": "microsoft.extensions.ambientmetadata.application.8.9.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FPWZAa9c0H4dvOj351iR1jkUIs4u9ykL4Bm592yhjDyO5lCoWd+TMAHx2EMbarzUvCvgjWjJIoC6//Q9kH6YhA==", "path": "microsoft.extensions.caching.abstractions/9.0.0", "hashPath": "microsoft.extensions.caching.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zbnPX/JQ0pETRSUG9fNPBvpIq42Aufvs15gGYyNIMhCun9yhmWihz0WgsI7bSDPjxWTKBf8oX/zv6v2uZ3W9OQ==", "path": "microsoft.extensions.caching.memory/9.0.0", "hashPath": "microsoft.extensions.caching.memory.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/8.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-fRTBd6+GourfQ+WMC7vYzyH9dHPH0mvnI9+zMo7MpmI1y17MLqB0HvjKOG9dmak4Cm1KL+I2kYmH3joXLkQknw==", "path": "microsoft.extensions.compliance.abstractions/8.9.1", "hashPath": "microsoft.extensions.compliance.abstractions.8.9.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "path": "microsoft.extensions.configuration/9.0.0", "hashPath": "microsoft.extensions.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.AzureKeyVault/3.1.24": {"type": "package", "serviceable": true, "sha512": "sha512-dAir8lfKn3Ukvis2sGddZ72bbuEQ24VusFELZg2JZDPTjBDyoVVocbxbe/39YAEHEq3TfCAIbHicCCKgHBBa2A==", "path": "microsoft.extensions.configuration.azurekeyvault/3.1.24", "hashPath": "microsoft.extensions.configuration.azurekeyvault.3.1.24.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qD+hdkBtR9Ps7AxfhTJCnoVakkadHgHlD1WRN0QHGHod+SDuca1ao1kF4G2rmpAz2AEKrE2N2vE8CCCZ+ILnNw==", "path": "microsoft.extensions.configuration.commandline/9.0.0", "hashPath": "microsoft.extensions.configuration.commandline.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v5R638eNMxksfXb7MFnkPwLPp+Ym4W/SIGNuoe8qFVVyvygQD5DdLusybmYSJEr9zc1UzWzim/ATKeIOVvOFDg==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4EK93Jcd2lQG4GY6PAw8jGss0ZzFP0vPc1J85mES5fKNuDTqgFXHba9onBw2s18fs3I4vdo2AWyfD1mPAxWSQQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WiTK0LrnsqmedrbzwL7f4ZUo+/wByqy2eKab39I380i2rd8ImfCRMrtkqJVGDmfqlkP/YzhckVOwPc5MPrSNpg==", "path": "microsoft.extensions.configuration.json/9.0.0", "hashPath": "microsoft.extensions.configuration.json.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FShWw8OysquwV7wQHYkkz0VWsJSo6ETUu4h7tJRMtnG0uR+tzKOldhcO8xB1pGSOI3Ng6v3N1Q94YO8Rzq1P6A==", "path": "microsoft.extensions.configuration.usersecrets/9.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "path": "microsoft.extensions.dependencyinjection/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/8.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-BH/s2h0IkRJQhMlNmG58YE2T1pRe8qBsB46SoWLNnjWTQb4rPWZ0nycz7phFAgZOhGuMvGLDM2oX3Tn6T9uTpA==", "path": "microsoft.extensions.dependencyinjection.autoactivation/8.9.1", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.8.9.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0CF9ZrNw5RAlRfbZuVIvzzhP8QeWqHiUmMBU/2H7Nmit8/vwP3/SbHeEctth7D4Gz2fBnEbokPc1NU8/j/1ZLw==", "path": "microsoft.extensions.diagnostics/9.0.0", "hashPath": "microsoft.extensions.diagnostics.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1K8P7XzuzX8W8pmXcZjcrqS6x5eSSdvhQohmcpgiQNY/HlDAlnrhR9dvlURfFz428A+RTCJpUyB+aKTA6AgVcQ==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/8.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-GwaU9Bljz4/yV5tk7lOyOo6ssW/3/jS6cocc7OBwwrvQBL1T8PRtaAzUKIQq0yDZE02WY5dGa0f+cSJ5R0wHsA==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/8.9.1", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.8.9.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3+ZUSpOSmie+o8NnLIRqCxSh65XL/ExU7JYnFOg58awDRlY3lVpZ9A369jkoZL1rpsq7LDhEfkn2ghhGaY1y5Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jGFKZiXs2HNseK3NK/rfwHNNovER71jSj4BD1a/649ml9+h6oEtYd0GSALZDNW8jZ2Rh+oAeadOa6sagYW1F2A==", "path": "microsoft.extensions.filesystemglobbing/9.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wNmQWRCa83HYbpxQ3wH7xBn8oyGjONSj1k8svzrFUFyJMfg/Ja/g0NfI0p85wxlUxBh97A6ypmL8X5vVUA5y2Q==", "path": "microsoft.extensions.hosting/9.0.0", "hashPath": "microsoft.extensions.hosting.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yUKJgu81ExjvqbNWqZKshBbLntZMbMVz/P7Way2SBx7bMqA08Mfdc9O7hWDKAiSp+zPUGT6LKcSCQIPeDK+CCw==", "path": "microsoft.extensions.hosting.abstractions/9.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DqI4q54U4hH7bIAq9M5a/hl5Odr/KBAoaZ0dcT4OgutD8dook34CbkvAfAIzkMVjYXiL+E5ul9etwwqiX4PHGw==", "path": "microsoft.extensions.http/9.0.0", "hashPath": "microsoft.extensions.http.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/8.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-OADwu5OBdV7xfCeqLY8oMGDkLo25jBFWRQz5pFia+IomcGQrB3xqKyfHA5yEcFnKQDe3r/hKOSLtRd4vnldK+w==", "path": "microsoft.extensions.http.diagnostics/8.9.1", "hashPath": "microsoft.extensions.http.diagnostics.8.9.1.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/8.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-WDoMPYEoVwb4cfKMpYFmeqEhSP1BclrskmvE9d2Ia8DDEDiX1ZZHbSiGtSiuR0J5+ubwyKeWZW2GaKkm05hCAA==", "path": "microsoft.extensions.http.resilience/8.9.1", "hashPath": "microsoft.extensions.http.resilience.8.9.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.ApplicationInsights/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-5OmXub+9MyX8FbqgO+hBJRHk1iJ+UZUU20oIU3wo+RbmH6Jtsja79rriHLlzlrkMzWbpCkCzF6f4Yb6iGbsDag==", "path": "microsoft.extensions.logging.applicationinsights/2.22.0", "hashPath": "microsoft.extensions.logging.applicationinsights.2.22.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-H05HiqaNmg6GjH34ocYE9Wm1twm3Oz2aXZko8GTwGBzM7op2brpAA8pJ5yyD1OpS1mXUtModBYOlcZ/wXeWsSg==", "path": "microsoft.extensions.logging.configuration/9.0.0", "hashPath": "microsoft.extensions.logging.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDZ4zsjl7N0K+R/1QTNpXBd79Kaf4qNLHtjk4NaG82UtNg2Z6etJywwv6OarOv3Rp7ocU7uIaRY4CrzHRO/d3w==", "path": "microsoft.extensions.logging.console/9.0.0", "hashPath": "microsoft.extensions.logging.console.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4wGlHsrLhYjLw4sFkfRixu2w4DK7dv60OjbvgbLGhUJk0eUPxYHhnszZ/P18nnAkfrPryvtOJ3ZTVev0kpqM6A==", "path": "microsoft.extensions.logging.debug/9.0.0", "hashPath": "microsoft.extensions.logging.debug.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/B8I5bScondnLMNULA3PBu/7Gvmv/P7L83j7gVrmLh6R+HCgHqUNIwVvzCok4ZjIXN2KxrsONHjFYwoBK5EJgQ==", "path": "microsoft.extensions.logging.eventlog/9.0.0", "hashPath": "microsoft.extensions.logging.eventlog.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zvSjdOAb3HW3aJPM5jf+PR9UoIkoci9id80RXmBgrDEozWI0GDw8tdmpyZgZSwFDvGCwHFodFLNQaeH8879rlA==", "path": "microsoft.extensions.logging.eventsource/9.0.0", "hashPath": "microsoft.extensions.logging.eventsource.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-wnjTFjEvvSbOs3iMfl6CeJcUgPHZMYUB9uAQbGQGxGwVRl4GydNpMSkVntTzoi7AqQeYumU9yDSNeVbpq+ebow==", "path": "microsoft.extensions.objectpool/8.0.8", "hashPath": "microsoft.extensions.objectpool.8.0.8.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ob3FXsXkcSMQmGZi7qP07EQ39kZpSBlTcAZLbJLdI4FIf0Jug8biv2HTavWmnTirchctPlq9bl/26CXtQRguzA==", "path": "microsoft.extensions.options.configurationextensions/9.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Resilience/8.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-tTEGuAvFFIfnjRUOokDdnh91WM/3pWfhgl7DsUFx9kAbXo6eF9G4i77eCLWtnBfqU977AUDQMaQuCM/KEXBtrg==", "path": "microsoft.extensions.resilience/8.9.1", "hashPath": "microsoft.extensions.resilience.8.9.1.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/8.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-dV9rjWv7mHQGhTPhJfFweMRiPQoG34lx4v2TcWK5F3u5Os09OVyp+V55F0a0bQPH+XM16aH5P6nzPnHjUGYKDw==", "path": "microsoft.extensions.telemetry/8.9.1", "hashPath": "microsoft.extensions.telemetry.8.9.1.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/8.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-bENN4kCy6f1SokJqwIrYba0Y/qa02j68GYMtvnbzks1JUWJmdHoylC9siI3R3duzQ0E2T1ugD0xdBiIMD0Rpiw==", "path": "microsoft.extensions.telemetry.abstractions/8.9.1", "hashPath": "microsoft.extensions.telemetry.abstractions.8.9.1.nupkg.sha512"}, "Microsoft.Graph/5.78.0": {"type": "package", "serviceable": true, "sha512": "sha512-8Zw6RmOcAEvAGmzNriyA91MinngteiecWTqDm2CWqHY5SFSekMiWe79ZPmqqgqDiiCnjGODuG0RgVYaQQ5xr2A==", "path": "microsoft.graph/5.78.0", "hashPath": "microsoft.graph.5.78.0.nupkg.sha512"}, "Microsoft.Graph.Core/3.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-0kBbgRiWUrrc7Um0oDcXl9t8Hxzgoz9SddErRDhqdDm4TWDlT8ItYuIfjwoFPHO1O51kwkDtTxzDRwZThbW5Uw==", "path": "microsoft.graph.core/3.2.4", "hashPath": "microsoft.graph.core.3.2.4.nupkg.sha512"}, "Microsoft.Identity.Client/4.71.1": {"type": "package", "serviceable": true, "sha512": "sha512-SgvSBcMRvmEEyV10pcvxNVUbwYoShmj/9pxXFVr3AFjE26IUzuwYLtLgt58xkEnT0xJBjfObaXxcol3BMtmEAg==", "path": "microsoft.identity.client/4.71.1", "hashPath": "microsoft.identity.client.4.71.1.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.71.1": {"type": "package", "serviceable": true, "sha512": "sha512-PGOHaoQhKBKnXy1kfW+Gu9/rxStKsqR+UZKeVv4XAsATdzmfj9y9kkUOftIjVFvxP3oh2Sk7v65ylS0K/qYADA==", "path": "microsoft.identity.client.extensions.msal/4.71.1", "hashPath": "microsoft.identity.client.extensions.msal.4.71.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-OwmvCXYTttrxV3qT7QKDkoQP4/DB4RWjTwEqV+dNfb2opHn29WGDzoF+r4BVFQVy+BDYMhRlhIp8g3jSyJd+4Q==", "path": "microsoft.identitymodel.abstractions/8.6.1", "hashPath": "microsoft.identitymodel.abstractions.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Clients.ActiveDirectory/3.14.2": {"type": "package", "serviceable": true, "sha512": "sha512-TNsJJMiRnkeby1ovThVoV9yFsPWjAdluwOA+Nf0LtSsBVVrKQv8Qp4kYOgyNwMVj+pDwbhXISySk+4HyHVWNZQ==", "path": "microsoft.identitymodel.clients.activedirectory/3.14.2", "hashPath": "microsoft.identitymodel.clients.activedirectory.3.14.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-CAu9DWsPZVtnyE3bOJ83rlPWpahY37sP/0bIOdRlxS90W88zSI4V3FyoCDlXxV8+gloT+a247pwPXfSNjYyAxw==", "path": "microsoft.identitymodel.jsonwebtokens/8.6.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-BdWlVgJYdmcR9TMUOhaZ3vJyaRO7zr7xgK+cRT4R2q59Xl7JMmTB4ctb/VOsyDhxXb497jDNNvLwldp+2ZVBEg==", "path": "microsoft.identitymodel.logging/8.6.1", "hashPath": "microsoft.identitymodel.logging.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-myul8Jm/kWOtbD+yDeU0LfDPGHDDhNO2Q6U40QlmL0LAK0u1wYl76yKM3/Mzv7ceOkporWAQoAb85QIFnXraOA==", "path": "microsoft.identitymodel.protocols/8.6.1", "hashPath": "microsoft.identitymodel.protocols.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-RTZIO/vZOPoy7dZzk3JfAD+EAWZg32xvcF7yNK8DcnIJy86OI1l2AIT7tZp0FG95cLrACV6X8xlc0StOfgB8ag==", "path": "microsoft.identitymodel.protocols.openidconnect/8.6.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-FvED2com8LIFl9yFXneiX0uxNf9fuf8jKDFcvxC93qXOAfFa8fdLkCiur1vWF+PvgQHhsHVBe6CtDZHzsN8nCQ==", "path": "microsoft.identitymodel.tokens/8.6.1", "hashPath": "microsoft.identitymodel.tokens.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Validators/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-FwST3dwbP4IgPsVVueMau8pHdFllesFSiZy+6L7/BtuflE8Tl1Z7MQW1/4ujmOOYQoBZCjdUQPzOFrC7NlqaBw==", "path": "microsoft.identitymodel.validators/8.6.1", "hashPath": "microsoft.identitymodel.validators.8.6.1.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irv0HuqoH8Ig5i2fO+8dmDNdFdsrO+DoQcedwIlb810qpZHBNQHZLW7C/AHBQDgLLpw2T96vmMAy/aE4Yj55Sg==", "path": "microsoft.io.recyclablememorystream/3.0.0", "hashPath": "microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512"}, "Microsoft.Kiota.Abstractions/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-nznpJUkC8F0iwZBael68nIS90g0wKgipJsLwarknd8hsGom0nJhyogZvIJUIzRWfNNSp0JhsxRYEW0WhvmppKw==", "path": "microsoft.kiota.abstractions/1.17.1", "hashPath": "microsoft.kiota.abstractions.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Authentication.Azure/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-BkzhCChAH+IOfH7NnB86ynvRqPfHRDdxQw1XJ/ShwsG66A9dRLJ/T4K+d+U5l4EiaCllgXDN6+TUEc5KXMgOgw==", "path": "microsoft.kiota.authentication.azure/1.17.1", "hashPath": "microsoft.kiota.authentication.azure.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Http.HttpClientLibrary/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-7cSZoEJ8G9YT3UxCILASZIB0GbmVnO3jdLDAMgMeU4O/SNemKtgA58pvKYJVrIZnJ9Up/mflQZFkGrSCOij4WQ==", "path": "microsoft.kiota.http.httpclientlibrary/1.17.1", "hashPath": "microsoft.kiota.http.httpclientlibrary.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Form/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-L35vwCSp01r7FoRunmSrKVfGnDBx1+3kcRnSMdleukRIAEeldnrNrjcitqVNSXhCqqnsVIT//B5dBvOCcZFCcA==", "path": "microsoft.kiota.serialization.form/1.17.1", "hashPath": "microsoft.kiota.serialization.form.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Json/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-EIUiZDVi1XS83k8ybtHMZr7NzHTtiIVGGIif6hiQV2CUEEhTQKxK/eggwajw8+CRfSPrs7ksvKNJleQnAIecHA==", "path": "microsoft.kiota.serialization.json/1.17.1", "hashPath": "microsoft.kiota.serialization.json.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Multipart/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-bbWg8iQrxBqPVRHfZnNuo1x9oHiRJJfRreTsAb9aO8ViO46wtB7niMgCWt69XG9iQdzAy9pKF8Quho14bNOAmg==", "path": "microsoft.kiota.serialization.multipart/1.17.1", "hashPath": "microsoft.kiota.serialization.multipart.1.17.1.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Text/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-dx1kIbBt21DqRt1tTPt3XkC8doEY1xFf+amPmPedCOM6X8IqdVs8xHymE/66aSWr5alufhEebu/bDxpR90M7mQ==", "path": "microsoft.kiota.serialization.text/1.17.1", "hashPath": "microsoft.kiota.serialization.text.1.17.1.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Rest.ClientRuntime/2.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-Hj96LBoCwKY2VQKfSCVGGPV1sSumVjuYnrlpBwL4JSTnSK4b6ZxjLtXj8LgmKav8xJ2gps+UN7eI3hHVFKvBFw==", "path": "microsoft.rest.clientruntime/2.3.8", "hashPath": "microsoft.rest.clientruntime.2.3.8.nupkg.sha512"}, "Microsoft.Rest.ClientRuntime.Azure/3.3.7": {"type": "package", "serviceable": true, "sha512": "sha512-6u8JIuvrztse4tPOcvNzAJuzGBP0uY+Ijggk8ZYhp0siGEZ1XfZylf1vpNGUicvwcrhhoIgDW73Z1L6QGssr2g==", "path": "microsoft.rest.clientruntime.azure/3.3.7", "hashPath": "microsoft.rest.clientruntime.azure.3.3.7.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lw1/VwLH1yxz6SfFEjVRCN0pnflLEsWgnV4qsdJ512/HhTwnKXUG+zDQ4yTO3K/EJQemGoNaBHX5InISNKTzUQ==", "path": "microsoft.win32.registry/4.3.0", "hashPath": "microsoft.win32.registry.4.3.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Polly.Core/8.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-bg4kE7mFwXc6FJ8NLknTgVgLAMlbToWC7vpdqAITv8lPzKpp9v7aWJPc04GRoZQaJhVY/tdr8K2/VW2aTmaA1Q==", "path": "polly.core/8.4.1", "hashPath": "polly.core.8.4.1.nupkg.sha512"}, "Polly.Extensions/8.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-NaRu+mopzJLoDm3qhklrUENIwkhmJbtzLRXK+oMb0c4bGwT84co+BM+TIwqApUfZrcz+BvA/vpB1vk6hB4XtAA==", "path": "polly.extensions/8.4.1", "hashPath": "polly.extensions.8.4.1.nupkg.sha512"}, "Polly.RateLimiting/8.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-YF9/pUUd3VZchjJ7+KWAINv5xtHlaWUvrhpGGC73He/zz0mRHzV7gKVDzqwAZrdDk09CdunA+Gt/a37Bl/rMwQ==", "path": "polly.ratelimiting/8.4.1", "hashPath": "polly.ratelimiting.8.4.1.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "SendGrid/9.29.3": {"type": "package", "serviceable": true, "sha512": "sha512-nb/zHePecN9U4/Bmct+O+lpgK994JklbCCNMIgGPOone/DngjQoMCHeTvkl+m0Nglvm0dqMEshmvB4fO8eF3dA==", "path": "sendgrid/9.29.3", "hashPath": "sendgrid.9.29.3.nupkg.sha512"}, "starkbank-ecdsa/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-OblOaKb1enXn+dSp7tsx9yjwV+/BEKM9jFhshIkZTwCk7LuTFTp+wSon6rFzuPiIiTGtvVWQNUw2slHjGktJog==", "path": "starkbank-ecdsa/1.3.3", "hashPath": "starkbank-ecdsa.1.3.3.nupkg.sha512"}, "Std.UriTemplate/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ix5VXZwLfolwVHyGTSSJl6KIJ2le6E9YjLdZBMS1Xxzw7VJankRvQW8JoUL69tEgfcw+0qjgWrlxANrhvS0QCQ==", "path": "std.uritemplate/2.0.1", "hashPath": "std.uritemplate.2.0.1.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ratu44uTIHgeBeI0dE8DWvmXVBSo4u7ozRZZHOMmK/JPpYyo0dAfgSiHlpiObMQ5lEtEyIXA40sKRYg5J6A8uQ==", "path": "system.buffers/4.3.0", "hashPath": "system.buffers.4.3.0.nupkg.sha512"}, "System.ClientModel/1.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-MY7eFGKp+Hu7Ciub8wigQ0odGrkml4eTjUy8d5Bu2eGAVvm8Qskkq+YuXiiS5wMJGq7iSvqseV4skd5WxTUdDA==", "path": "system.clientmodel/1.4.1", "hashPath": "system.clientmodel.1.4.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "path": "system.configuration.configurationmanager/6.0.0", "hashPath": "system.configuration.configurationmanager.6.0.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ddppcFpnbohLWdYKr/ZeLZHmmI+DXFgZ3Snq+/E7SwcdW4UnvxmaugkwGywvGVWkHPGCSZjCP+MLzu23AL5SDw==", "path": "system.diagnostics.diagnosticsource/9.0.0", "hashPath": "system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qd01+AqPhbAG14KtdtIqFk+cxHQFZ/oqRSCoxU1F+Q6Kv0cl726sl7RzU9yLFGd4BUOKdN4XojXF0pQf/R6YeA==", "path": "system.diagnostics.eventlog/9.0.0", "hashPath": "system.diagnostics.eventlog.9.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gbeE5tNp/oB7O8kTTLh3wPPJCxpNOphXPTWVs1BsYuFOYapFijWuh0LYw1qnDo4gwDUYPXOmpTIhvtxisGsYOQ==", "path": "system.diagnostics.performancecounter/6.0.0", "hashPath": "system.diagnostics.performancecounter.6.0.0.nupkg.sha512"}, "System.Diagnostics.Process/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-J0wOX07+QASQblsfxmIMFc9Iq7KTXYL3zs2G/Xc704Ylv3NpuVdo6gij6V3PGiptTxqsK0K7CdXenRvKUnkA2g==", "path": "system.diagnostics.process/4.3.0", "hashPath": "system.diagnostics.process.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-EXL1Tj+pizswtHHPiQyNumrTo8XOLX7SoTm7Bz00/DyiIoG2H/kQItoajSvr1MYtvDNXveqULsoWDoJFI3aHzQ==", "path": "system.identitymodel.tokens.jwt/8.6.1", "hashPath": "system.identitymodel.tokens.jwt.8.6.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-vMToiarpU81LR1/KZtnT7VDPvqAZfw9oOS5nY6pPP78nGYz3COLsQH3OfzbR+SjTgltd31R6KmKklz/zDpTmzw==", "path": "system.io.filesystem.accesscontrol/4.7.0", "hashPath": "system.io.filesystem.accesscontrol.4.7.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Hashing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "path": "system.io.hashing/6.0.0", "hashPath": "system.io.hashing.6.0.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eA3cinogwaNB4jdjQHOP3Z3EuyiDII7MT35jgtnsA4vkn0LUrrSHsU0nzHTzFzmaFYeKV7MYyMxOocFzsBHpTw==", "path": "system.io.pipelines/9.0.0", "hashPath": "system.io.pipelines.9.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-yliDgLh9S9Mcy5hBIdZmX6yphYIW3NH+3HN1kV1m7V1e0s7LNTw/tHNjJP4U9nSMEgl3w1TzYv/KA1Tg9NYy6w==", "path": "system.memory.data/6.0.1", "hashPath": "system.memory.data.6.0.1.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.DataContractSerialization/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lcqFBUaCZxPiUkA4dlSOoPZGtZsAuuElH2XHgLwGLxd7ZozWetV5yiz0qGAV2AUYOqw97MtZBjbLMN16Xz4vXA==", "path": "system.private.datacontractserialization/4.1.1", "hashPath": "system.private.datacontractserialization.4.1.1.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Json/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-+7DIJhnKYgCzUgcLbVTtRQb2l1M0FP549XFlFkQM5lmNiUBl44AfNbx4bz61xA8PzLtlYwfmif4JJJW7MPPnjg==", "path": "system.runtime.serialization.json/4.0.2", "hashPath": "system.runtime.serialization.json.4.0.2.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-HZ6Du5QrTG8MNJbf4e4qMO3JRAkIboGT5Fk804uZtg3Gq516S7hAqTm2UZKUHa7/6HUGdVy3AqMQKbns06G/cg==", "path": "system.runtime.serialization.primitives/4.1.1", "hashPath": "system.runtime.serialization.primitives.4.1.1.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2hMgAErLbKyUUwt18qSBf9T5Y+SFAL3ZedM8fLupkVj8Rj2PZ9oxQ37XX2LF8fTO1wNIxvKpihD7Of7D/NxZw==", "path": "system.text.encodings.web/9.0.0", "hashPath": "system.text.encodings.web.9.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2eZu6IP+etFVBBFUFzw2w6J21DqIN5eL9Y8r8JfJWUmV28Z5P0SNU01oCisVHQgHsDhHPnmq2s1hJrJCFZWloQ==", "path": "system.xml.xmldocument/4.0.1", "hashPath": "system.xml.xmldocument.4.0.1.nupkg.sha512"}, "System.Xml.XmlSerializer/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-FrazwwqfIXTfq23mfv4zH+BjqkSFNaNFBtjzu3I9NRmG8EELYyrv/fJnttCIwRMFRR/YKXF1hmsMmMEnl55HGw==", "path": "system.xml.xmlserializer/4.0.11", "hashPath": "system.xml.xmlserializer.4.0.11.nupkg.sha512"}}}